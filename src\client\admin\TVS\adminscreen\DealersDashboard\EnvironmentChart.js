import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";

const LegalComplianceChart = ({ data }) => {
  const filterDataByCategory = (category) =>
    data.filter((entry) => entry.category === category);

  const aggregateData = (filteredData, categoryType) => {
    const totals = {
      legal_compliance: 0,
      water_management: 0,
      wastewater_management: 0,
      waste_management: 0,
      energy_management: 0,
    };

    const maxValues =
      categoryType === "Service"
        ? {
            legal_compliance: 10,
            water_management: 10,
            wastewater_management: 5,
            waste_management: 5,
            energy_management: 10,
          }
        : {
            legal_compliance: 10,
            water_management: 10,
            wastewater_management: 0,
            waste_management: 10,
            energy_management: 10,
          };

    const count = filteredData.length || 1;

    filteredData.forEach((entry) => {
      totals.legal_compliance += isNaN(entry.legal_compliance) ? 0 : entry.legal_compliance;
      totals.water_management += isNaN(entry.water_management) ? 0 : entry.water_management;
      totals.wastewater_management += isNaN(parseFloat(entry.wastewater_management)) ? 0 : parseFloat(entry.wastewater_management);
      totals.waste_management += isNaN(entry.waste_management) ? 0 : entry.waste_management;
      totals.energy_management += isNaN(entry.energy_management) ? 0 : entry.energy_management;
    });

    const clampForGraph = (value) => Math.max(0, value);
    const clampRemaining = (avg, max) => Math.max(0, Math.min(max, max - avg));

    return [
      {
        category: "Legal Compliance",
        avgValue: totals.legal_compliance / count,
        avgValueForGraph: clampForGraph(totals.legal_compliance / count),
        maxValue: maxValues.legal_compliance,
        remainingToMax: clampRemaining(totals.legal_compliance / count, maxValues.legal_compliance),
      },
      {
        category: "Water Management",
        avgValue: totals.water_management / count,
        avgValueForGraph: clampForGraph(totals.water_management / count),
        maxValue: maxValues.water_management,
        remainingToMax: clampRemaining(totals.water_management / count, maxValues.water_management),
      },
      {
        category: "Wastewater Management",
        avgValue: totals.wastewater_management / count,
        avgValueForGraph: clampForGraph(totals.wastewater_management / count),
        maxValue: maxValues.wastewater_management,
        remainingToMax: clampRemaining(totals.wastewater_management / count, maxValues.wastewater_management),
      },
      {
        category: "Waste Management",
        avgValue: totals.waste_management / count,
        avgValueForGraph: clampForGraph(totals.waste_management / count),
        maxValue: maxValues.waste_management,
        remainingToMax: clampRemaining(totals.waste_management / count, maxValues.waste_management),
      },
      {
        category: "Energy Management",
        avgValue: totals.energy_management / count,
        avgValueForGraph: clampForGraph(totals.energy_management / count),
        maxValue: maxValues.energy_management,
        remainingToMax: clampRemaining(totals.energy_management / count, maxValues.energy_management),
      },
    ];
  };

  const salesData = aggregateData(filterDataByCategory("Sales"), "Sales");
  const serviceData = aggregateData(filterDataByCategory("Service"), "Service");

  const getYAxisDomain = () => [0, 10];

  const wrapText = (text, width = 12) => {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + " " + word).length > width) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine += (currentLine ? " " : "") + word;
      }
    });

    if (currentLine) lines.push(currentLine);
    return lines;
  };

  const CustomizedTick = ({ x, y, payload }) => {
    const lines = wrapText(payload.value, 12);
    const lineHeight = 14;
    return (
      <g transform={`translate(${x},${y + 10})`}>
        {lines.map((line, index) => (
          <text
            key={index}
            x={0}
            y={index * lineHeight}
            textAnchor="middle"
            fill="#555"
            fontSize={11}
            fontFamily="Lato"
          >
            {line}
          </text>
        ))}
      </g>
    );
  };

  const CustomLegend = ({ payload }) => (
    <ul style={{ display: "flex", justifyContent: "center", padding: 0, listStyle: "none" }}>
      {payload.map((entry, index) => (
        <li key={index} style={{ marginRight: 10, display: "flex", alignItems: "center" }}>
          <span
            style={{
              display: "inline-block",
              backgroundColor: entry.color,
              width: 10,
              height: 10,
              borderRadius: "50%",
              marginRight: 5,
              marginTop: "22px",
            }}
          />
          <span style={{ marginTop: "22px", fontSize: 14, color: "#555" }}>{entry.value}</span>
        </li>
      ))}
    </ul>
  );

  const renderBarChart = (chartData, title) => (
    <div style={{ width: "100%", height: 400 }}>
      <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>{title}</h3>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} barSize={50} margin={{ top: 20, right: 30, left: 20, bottom: 25 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="category" tick={<CustomizedTick />} interval={0} tickLine />
          <YAxis domain={getYAxisDomain()} />
          <Tooltip
            formatter={(value, name, props) => {
              const { payload } = props;
              if (name === "Achieved") {
                return [`${payload.avgValue.toFixed(2)} / ${payload.maxValue}`, "Achieved"];
              }
              return [null, null];
            }}
          />
          <Legend content={CustomLegend} />
          <Bar
            dataKey="avgValueForGraph"
            stackId="progress"
            fill="#2C7C69"
            name="Achieved"
            label={{
              position: "insideTop",
              fill: "#fff",
              fontSize: 12,
              formatter: (val) => (val ? val.toFixed(1) : ""),
            }}
          />
          <Bar dataKey="remainingToMax" stackId="progress" fill="#A9D7C9" name="Remaining" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );

  return (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
        gap: "20px",
        width: "100%",
        padding: "20px",
      }}
    >
      {renderBarChart(salesData, "Sales - Environment")}
      {renderBarChart(serviceData, "Service - Environment")}
    </div>
  );
};

export default LegalComplianceChart;
