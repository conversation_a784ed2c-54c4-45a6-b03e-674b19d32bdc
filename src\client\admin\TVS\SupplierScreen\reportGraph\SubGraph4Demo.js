import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>,
  Legend,
  CartesianGrid,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import CriticalNonCompliances from "../../adminscreen/SupplyChain/NonComplianceComponent";

// Function to wrap long text into multiple lines

const SubGraph4Demo = ({ supplyData,count=0 }) => {
  const [chartData, setChartData] = useState([]);
console.log(supplyData)
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "#fff",
            border: "1px solid #ccc",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "14px",
            fontFamily: "Lato",
            lineHeight: "1.5",
          }}
        >
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map((entry) => (
            <p key={entry.name} style={{ margin: 0, color: "black" }}>{`${entry.name
              }:${entry.name === "Maximum" ? 20 : entry.value}`}</p>
          ))}
        </div>
      );
    }

    return null;
  };

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
          marginTop: "10px",
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,

              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  useEffect(() => {
    if (supplyData.length > 0) {
      // Compute average scores for social factors
      const totalSuppliers = 1

      const totalHealthSafety = supplyData.find(x => x.id === '6794b6f5-69eb-48a7-baf1-22b5db337504')?.sectionTotalScore || 0
      const totalSocialStewardship = supplyData.find(x => x.id === 'bce965ce-13a6-476a-8f7b-f2bcea2a3593')?.sectionTotalScore || 0
      const totalSustainability = supplyData.find(x => x.id === '7884432d-9f75-42d6-a49f-fa6c6cd08fcd')?.sectionTotalScore || 0

      const avgHealthSafety = (totalHealthSafety / totalSuppliers).toFixed(1);
      const avgSocialStewardship = (
        totalSocialStewardship / totalSuppliers
      ).toFixed(1);
      const avgSustainability = (totalSustainability / totalSuppliers).toFixed(
        1
      );

      setChartData([
        {
          category: "Occupational Health & Safety",
          maxScore: 20 - avgHealthSafety,
          avgScore: avgHealthSafety,
        },
        {
          category: "Supplier Social Stewardship Framework",
          maxScore: 10 - avgSocialStewardship,
          avgScore: avgSocialStewardship,
        },
        {
          category: "Supplier Sustainability Ambassadorship Framework",
          maxScore: 20 - avgSustainability,
          avgScore: avgSustainability,
        },
      ]);
    }
  }, [supplyData]);

  const CustomizedTick = ({ x, y, payload }) => {
    return (
      <g transform={`translate(${x},${y})`}>
        <text
          textAnchor="middle"
          fontSize={10}
          fill="#666"
          dominantBaseline="middle"
        >
          {wrapText(payload.value, 20)} {/* Wrap text with custom width */}
        </text>
      </g>
    );
  };

  const wrapText = (text, width = 40) => {
    let words = text.split(" ");
    let lines = [];
    let currentLine = "";

    words.forEach((word) => {
      if ((currentLine + " " + word).length > width) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine += (currentLine ? " " : "") + word;
      }
    });

    lines.push(currentLine); // Push the remaining line
    return lines.map((line, index) => (
      <tspan key={index} x="0" dy={index === 0 ? 0 : 10}>
        {line}
      </tspan>
    ));
  };

  return (
    <div className="container mt-4 pt-2" style={{background:'#FDF1EB'}}>
      <h5 className="mb-3 text-center text-dark">Social Section Performance</h5>

      <ResponsiveContainer
        width="100%"
        height={300}

      >
        <BarChart barSize={50} data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="category"
            fontSize={12} // Set font size to 6
            interval={0} // Ensure all labels are displayed
            tick={<CustomizedTick />} // Custom tick rendering
          />
          <YAxis domain={[0, 20]} />
          <Tooltip content={CustomTooltip} />
          <Legend content={CustomLegend} />
          <Bar dataKey="avgScore" stackId="a" fill="#FC6E51" name=" Achieved">
            <LabelList
              dataKey="avgScore"
              position="insideBottom"
              style={{ fontSize: "12px", fill: "white" }}
            />
          </Bar>
          <Bar
            dataKey="maxScore"
            stackId="a"
            fill="#FEB2A8"
            name="Maximum"
          ></Bar>
        </BarChart>
      </ResponsiveContainer>
      <div className="col-12 flex justify-content-center">
        <CriticalNonCompliances count={count} />

      </div>
    </div>
  );
};

// Custom tick rendering for wrapping text

export default SubGraph4Demo;
